/**
 * 命令执行工具
 */

import { spawn, exec } from 'child_process';
import { promisify } from 'util';
import { logger } from './logger';

const execAsync = promisify(exec);

/**
 * 命令执行选项
 */
export interface CommandOptions {
  cwd?: string;
  timeout?: number;
  env?: Record<string, string>;
  shell?: boolean;
}

/**
 * 命令执行结果
 */
export interface CommandResult {
  stdout: string;
  stderr: string;
  exitCode: number;
  success: boolean;
}

/**
 * 执行命令并返回结果
 */
export async function executeCommand(
  command: string,
  args: string[] = [],
  options: CommandOptions = {}
): Promise<CommandResult> {
  const { cwd, timeout = 30000, env, shell = false } = options;
  
  return new Promise((resolve, reject) => {
    const child = spawn(command, args, {
      cwd,
      env: { ...process.env, ...env },
      shell,
      stdio: ['pipe', 'pipe', 'pipe'],
    });

    let stdout = '';
    let stderr = '';
    let timeoutId: NodeJS.Timeout | null = null;

    // 设置超时
    if (timeout > 0) {
      timeoutId = setTimeout(() => {
        child.kill('SIGTERM');
        reject(new Error(`Command timeout after ${timeout}ms: ${command} ${args.join(' ')}`));
      }, timeout);
    }

    // 收集输出
    child.stdout?.on('data', (data) => {
      stdout += data.toString();
    });

    child.stderr?.on('data', (data) => {
      stderr += data.toString();
    });

    // 处理进程结束
    child.on('close', (code) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }

      const result: CommandResult = {
        stdout: stdout.trim(),
        stderr: stderr.trim(),
        exitCode: code || 0,
        success: (code || 0) === 0,
      };

      logger.debug(`Command executed: ${command} ${args.join(' ')}`, {
        exitCode: result.exitCode,
        success: result.success,
      });

      resolve(result);
    });

    // 处理错误
    child.on('error', (error) => {
      if (timeoutId) {
        clearTimeout(timeoutId);
      }
      reject(error);
    });
  });
}

/**
 * 执行shell命令
 */
export async function executeShellCommand(
  command: string,
  options: CommandOptions = {}
): Promise<CommandResult> {
  const { cwd, timeout = 30000, env } = options;

  try {
    const { stdout, stderr } = await execAsync(command, {
      cwd,
      timeout,
      env: { ...process.env, ...env },
    });

    return {
      stdout: stdout.trim(),
      stderr: stderr.trim(),
      exitCode: 0,
      success: true,
    };
  } catch (error: any) {
    return {
      stdout: error.stdout?.trim() || '',
      stderr: error.stderr?.trim() || error.message,
      exitCode: error.code || 1,
      success: false,
    };
  }
}

/**
 * 检查命令是否可用
 */
export async function isCommandAvailable(command: string): Promise<boolean> {
  try {
    const result = await executeShellCommand(`${command} --version`, { timeout: 5000 });
    return result.success;
  } catch {
    return false;
  }
}

/**
 * 检查MinerU是否安装
 */
export async function checkMineruInstallation(): Promise<boolean> {
  try {
    const result = await executeShellCommand('mineru --version', { timeout: 5000 });
    return result.success;
  } catch {
    return false;
  }
}

/**
 * 检查LibreOffice是否安装
 */
export async function checkLibreOfficeInstallation(): Promise<boolean> {
  const commands = ['libreoffice', 'soffice'];
  
  for (const cmd of commands) {
    if (await isCommandAvailable(cmd)) {
      return true;
    }
  }
  
  return false;
}

/**
 * 构建MinerU命令
 */
export function buildMineruCommand(
  inputPath: string,
  outputDir: string,
  options: {
    method?: string;
    lang?: string;
    backend?: string;
    startPage?: number;
    endPage?: number;
    formula?: boolean;
    table?: boolean;
    device?: string;
    source?: string;
  } = {}
): string[] {
  const {
    method = 'auto',
    lang,
    backend = 'pipeline',
    startPage,
    endPage,
    formula = true,
    table = true,
    device,
    source = 'huggingface',
  } = options;

  const args = [
    '-p', inputPath,
    '-o', outputDir,
    '-m', method,
    '-b', backend,
    '--source', source,
  ];

  if (lang) {
    args.push('-l', lang);
  }

  if (startPage !== undefined) {
    args.push('-s', startPage.toString());
  }

  if (endPage !== undefined) {
    args.push('-e', endPage.toString());
  }

  if (!formula) {
    args.push('--no-formula');
  }

  if (!table) {
    args.push('--no-table');
  }

  if (device) {
    args.push('--device', device);
  }

  return args;
}
