/**
 * 图像模态处理器
 */

import fs from 'fs-extra';
import path from 'path';
import sharp from 'sharp';
import { 
  MultimodalContent, 
  EntityInfo, 
  ContentType,
  ImageContent,
  VisionModelFunction,
  ProcessError
} from '@/types';
import { ImageProcessorConfig } from '@/types/processors';
import { BaseModalProcessor } from './BaseModalProcessor';
import { fileExists } from '@/utils/fileUtils';
import { getModuleLogger } from '@/utils/logger';

const logger = getModuleLogger('ImageModalProcessor');

/**
 * 图像模态处理器
 */
export class ImageModalProcessor extends BaseModalProcessor {
  private visionModelFunc: VisionModelFunction;

  constructor(config: ImageProcessorConfig) {
    super(config);
    this.visionModelFunc = config.modalCaptionFunc;
  }

  /**
   * 处理图像内容
   */
  async processMultimodalContent(
    modalContent: MultimodalContent,
    contentType: ContentType,
    filePath?: string,
    entityName?: string
  ): Promise<[string, EntityInfo]> {
    this.validateContent(modalContent);
    
    if (contentType !== 'image') {
      throw new ProcessError(`Expected image content, got ${contentType}`, contentType);
    }

    const imageContent = modalContent as ImageContent;
    const startTime = Date.now();

    try {
      logger.info(`Processing image content: ${imageContent.img_path}`);

      // 生成实体名称
      const finalEntityName = entityName || this.generateDefaultEntityName(imageContent, filePath);

      // 生成增强描述
      const enhancedDescription = await this.generateImageDescription(imageContent);

      // 提取实体信息
      const entityInfo = await this.extractEntityInfo(
        enhancedDescription,
        finalEntityName,
        'IMAGE_ENTITY_EXTRACTION'
      );

      // 创建实体和文本块
      const result = await this.createEntityAndChunk(
        enhancedDescription,
        entityInfo,
        filePath || 'unknown'
      );

      const processingTime = Date.now() - startTime;
      logger.info(`Image processing completed in ${processingTime}ms`);

      return result;

    } catch (error) {
      logger.error('Failed to process image content:', error);
      throw new ProcessError(
        `Failed to process image content: ${error instanceof Error ? error.message : String(error)}`,
        'image'
      );
    }
  }

  /**
   * 生成图像描述
   */
  private async generateImageDescription(imageContent: ImageContent): Promise<string> {
    try {
      // 读取图像数据
      const imageData = await this.loadImageAsBase64(imageContent.img_path);
      
      // 构建提示词变量
      const variables = {
        image_description: imageContent.img_path,
        image_caption: imageContent.img_caption?.join(', ') || '',
        image_footnote: imageContent.img_footnote?.join(', ') || ''
      };

      // 生成描述
      const enhancedDescription = await this.generateEnhancedDescription(
        imageContent,
        'IMAGE_ANALYSIS'
      );

      return enhancedDescription;

    } catch (error) {
      logger.error('Failed to generate image description:', error);
      
      // 返回基础描述
      return this.generateFallbackDescription(imageContent);
    }
  }

  /**
   * 加载图像为Base64格式
   */
  private async loadImageAsBase64(imagePath: string): Promise<string> {
    try {
      if (!await fileExists(imagePath)) {
        throw new Error(`Image file not found: ${imagePath}`);
      }

      // 读取图像文件
      const imageBuffer = await fs.readFile(imagePath);
      
      // 使用Sharp处理图像（可选：调整大小、格式转换等）
      const processedBuffer = await sharp(imageBuffer)
        .resize(1024, 1024, { 
          fit: 'inside', 
          withoutEnlargement: true 
        })
        .jpeg({ quality: 80 })
        .toBuffer();

      // 转换为Base64
      return processedBuffer.toString('base64');

    } catch (error) {
      logger.error(`Failed to load image as base64: ${imagePath}`, error);
      throw new ProcessError(
        `Failed to load image: ${error instanceof Error ? error.message : String(error)}`,
        'image'
      );
    }
  }

  /**
   * 提取图像数据（重写基类方法）
   */
  protected async extractImageData(content: MultimodalContent): Promise<string | undefined> {
    if (content.type !== 'image') {
      return undefined;
    }

    const imageContent = content as ImageContent;
    try {
      return await this.loadImageAsBase64(imageContent.img_path);
    } catch (error) {
      logger.warn('Failed to extract image data:', error);
      return undefined;
    }
  }

  /**
   * 生成备用描述
   */
  private generateFallbackDescription(imageContent: ImageContent): string {
    const parts: string[] = [];
    
    parts.push(`图像文件: ${path.basename(imageContent.img_path)}`);
    
    if (imageContent.img_caption && imageContent.img_caption.length > 0) {
      parts.push(`标题: ${imageContent.img_caption.join(', ')}`);
    }
    
    if (imageContent.img_footnote && imageContent.img_footnote.length > 0) {
      parts.push(`注释: ${imageContent.img_footnote.join(', ')}`);
    }
    
    if (imageContent.bbox) {
      parts.push(`位置: [${imageContent.bbox.join(', ')}]`);
    }

    return parts.join('\n');
  }

  /**
   * 获取图像元数据
   */
  private async getImageMetadata(imagePath: string): Promise<any> {
    try {
      if (!await fileExists(imagePath)) {
        return null;
      }

      const metadata = await sharp(imagePath).metadata();
      return {
        width: metadata.width,
        height: metadata.height,
        format: metadata.format,
        size: metadata.size,
        density: metadata.density,
        hasAlpha: metadata.hasAlpha
      };

    } catch (error) {
      logger.warn(`Failed to get image metadata: ${imagePath}`, error);
      return null;
    }
  }

  /**
   * 验证图像内容
   */
  protected validateContent(content: MultimodalContent): void {
    super.validateContent(content);
    
    const imageContent = content as ImageContent;
    
    if (!imageContent.img_path) {
      throw new ProcessError('Invalid image content: img_path is required', 'image');
    }
  }

  /**
   * 检查图像文件是否有效
   */
  private async isValidImageFile(imagePath: string): Promise<boolean> {
    try {
      if (!await fileExists(imagePath)) {
        return false;
      }

      // 尝试读取图像元数据来验证文件
      await sharp(imagePath).metadata();
      return true;

    } catch (error) {
      logger.warn(`Invalid image file: ${imagePath}`, error);
      return false;
    }
  }

  /**
   * 获取支持的图像格式
   */
  static getSupportedFormats(): string[] {
    return [
      '.jpg', '.jpeg', '.png', '.bmp', 
      '.tiff', '.tif', '.gif', '.webp',
      '.svg', '.ico'
    ];
  }

  /**
   * 检查文件是否为支持的图像格式
   */
  static isSupportedImageFormat(filePath: string): boolean {
    const ext = path.extname(filePath).toLowerCase();
    return this.getSupportedFormats().includes(ext);
  }
}
