# RAG-Anything TypeScript

🚀 **RAG-Anything的TypeScript实现** - 全能多模态RAG系统

## 概述

这是RAG-Anything项目的TypeScript重构版本，提供了与原Python版本相同的功能，但具有更好的类型安全性和现代JavaScript生态系统的支持。

## 主要特性

- 🔄 **端到端多模态管道** - 从文档摄取解析到智能多模态查询回答的完整工作流
- 📄 **通用文档支持** - 无缝处理PDF、Office文档、图像和各种文件格式
- 🧠 **专业内容分析** - 针对图像、表格、数学公式和异构内容类型的专用处理器
- 🔗 **多模态知识图谱** - 自动实体提取和跨模态关系发现，增强理解能力
- ⚡ **自适应处理模式** - 灵活的MinerU解析或直接多模态内容注入工作流
- 🎯 **混合智能检索** - 跨文本和多模态内容的高级搜索功能，具有上下文理解

## 技术栈

- **TypeScript** - 类型安全的JavaScript超集
- **Node.js** - 服务器端JavaScript运行时
- **Sharp** - 高性能图像处理
- **PDF-Parse** - PDF文档解析
- **Mammoth** - Word文档处理
- **XLSX** - Excel文档处理

## 快速开始

### 安装依赖

```bash
npm install
```

### 构建项目

```bash
npm run build
```

### 运行示例

```bash
npm run dev
```

### 运行测试

```bash
npm test
```

## 项目结构

```
src/
├── types/          # TypeScript类型定义
├── utils/          # 工具函数
├── parsers/        # 文档解析器
├── processors/     # 多模态处理器
├── core/           # 核心RAG功能
├── examples/       # 使用示例
└── test/           # 测试文件
```

## 使用方法

```typescript
import { RAGAnything } from './core/RAGAnything';

const rag = new RAGAnything({
  workingDir: './rag_storage',
  llmModelFunc: yourLLMFunction,
  visionModelFunc: yourVisionFunction,
  embeddingFunc: yourEmbeddingFunction,
});

// 处理文档
await rag.processDocumentComplete('path/to/document.pdf');

// 查询
const result = await rag.queryWithMultimodal('你的问题', 'hybrid');
```

## 许可证

MIT License

## 贡献

欢迎提交Issue和Pull Request！
