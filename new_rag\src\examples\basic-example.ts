/**
 * RAG-Anything基础使用示例
 */

import { RAGAnything } from '../core';
import { LLMModelFunction, VisionModelFunction, EmbeddingFunction } from '../types';
import { logger } from '../utils/logger';

// 模拟的LLM函数
const mockLLMFunction: LLMModelFunction = async (
  prompt: string,
  systemPrompt?: string,
  historyMessages?: any[]
): Promise<string> => {
  // 这里应该调用实际的LLM API，如OpenAI、Claude等
  logger.info('Mock LLM called', { prompt: prompt.substring(0, 100) });
  
  return `这是一个模拟的LLM响应，针对提示: ${prompt.substring(0, 50)}...`;
};

// 模拟的视觉模型函数
const mockVisionFunction: VisionModelFunction = async (
  prompt: string,
  systemPrompt?: string,
  historyMessages?: any[],
  imageData?: string
): Promise<string> => {
  // 这里应该调用实际的视觉模型API，如GPT-4V、Claude Vision等
  logger.info('Mock Vision model called', { 
    prompt: prompt.substring(0, 100),
    hasImage: !!imageData 
  });
  
  return `这是一个模拟的视觉模型响应，针对图像和提示: ${prompt.substring(0, 50)}...`;
};

// 模拟的嵌入函数
const mockEmbeddingFunction: EmbeddingFunction = async (
  texts: string[]
): Promise<number[][]> => {
  // 这里应该调用实际的嵌入模型API，如OpenAI Embeddings
  logger.info('Mock Embedding called', { textCount: texts.length });
  
  // 返回模拟的嵌入向量（1536维）
  return texts.map(() => Array.from({ length: 1536 }, () => Math.random()));
};

/**
 * 基础使用示例
 */
async function basicExample(): Promise<void> {
  try {
    logger.info('Starting basic RAG-Anything example');

    // 1. 创建RAGAnything实例
    const rag = new RAGAnything({
      workingDir: './rag_storage',
      llmModelFunc: mockLLMFunction,
      visionModelFunc: mockVisionFunction,
      embeddingFunc: mockEmbeddingFunction
    });

    // 2. 初始化
    await rag.initialize();
    logger.info('RAGAnything initialized successfully');

    // 3. 检查MinerU安装状态
    const mineruInstalled = await rag.checkMineruInstallation();
    logger.info(`MinerU installation status: ${mineruInstalled}`);

    // 4. 获取处理器信息
    const processorInfo = rag.getProcessorInfo();
    logger.info('Processor information:', processorInfo);

    // 5. 处理单个文档（如果有的话）
    const testFilePath = './test-document.pdf';
    try {
      await rag.processDocumentComplete(testFilePath, {
        outputDir: './output',
        parseMethod: 'auto',
        displayStats: true
      });
      logger.info(`Document processed successfully: ${testFilePath}`);
    } catch (error) {
      logger.warn(`Test document not found or processing failed: ${testFilePath}`);
    }

    // 6. 执行查询
    const queryResult = await rag.queryWithMultimodal(
      '请总结文档中的主要内容和图表信息',
      { mode: 'hybrid' }
    );
    
    logger.info('Query result:', queryResult);

    // 7. 清理资源
    await rag.cleanup();
    logger.info('RAGAnything cleanup completed');

  } catch (error) {
    logger.error('Basic example failed:', error);
    throw error;
  }
}

/**
 * 批处理示例
 */
async function batchProcessingExample(): Promise<void> {
  try {
    logger.info('Starting batch processing example');

    const rag = new RAGAnything({
      workingDir: './rag_storage_batch',
      llmModelFunc: mockLLMFunction,
      visionModelFunc: mockVisionFunction,
      embeddingFunc: mockEmbeddingFunction
    });

    await rag.initialize();

    // 批量处理文件夹
    const batchResult = await rag.processFolderComplete('./documents', {
      outputDir: './batch_output',
      fileExtensions: ['.pdf', '.docx', '.jpg', '.png'],
      recursive: true,
      maxWorkers: 2,
      parseMethod: 'auto',
      displayStats: false
    });

    logger.info('Batch processing completed:', batchResult);

    await rag.cleanup();

  } catch (error) {
    logger.error('Batch processing example failed:', error);
    throw error;
  }
}

/**
 * 自定义处理器示例
 */
async function customProcessorExample(): Promise<void> {
  try {
    logger.info('Starting custom processor example');

    const rag = new RAGAnything({
      workingDir: './rag_storage_custom',
      llmModelFunc: mockLLMFunction,
      visionModelFunc: mockVisionFunction,
      embeddingFunc: mockEmbeddingFunction
    });

    await rag.initialize();

    // 这里可以展示如何扩展处理器或添加自定义逻辑
    logger.info('Custom processor example - processors available:', 
      rag.getProcessorInfo().processors);

    await rag.cleanup();

  } catch (error) {
    logger.error('Custom processor example failed:', error);
    throw error;
  }
}

// 主函数
async function main(): Promise<void> {
  try {
    // 设置日志级别
    logger.level = 'info';

    logger.info('=== RAG-Anything TypeScript Examples ===');

    // 运行基础示例
    await basicExample();
    
    logger.info('\n=== Batch Processing Example ===');
    await batchProcessingExample();
    
    logger.info('\n=== Custom Processor Example ===');
    await customProcessorExample();

    logger.info('All examples completed successfully!');

  } catch (error) {
    logger.error('Examples failed:', error);
    process.exit(1);
  }
}

// 如果直接运行此文件
if (require.main === module) {
  main().catch(error => {
    console.error('Fatal error:', error);
    process.exit(1);
  });
}

export {
  basicExample,
  batchProcessingExample,
  customProcessorExample,
  mockLLMFunction,
  mockVisionFunction,
  mockEmbeddingFunction
};
