/**
 * 处理器相关类型定义
 */

import { 
  MultimodalContent, 
  EntityInfo, 
  LLMModelFunction, 
  VisionModelFunction,
  ContentType 
} from './index';

/**
 * 基础处理器接口
 */
export interface BaseProcessor {
  /**
   * 处理多模态内容
   */
  processMultimodalContent(
    modalContent: MultimodalContent,
    contentType: ContentType,
    filePath?: string,
    entityName?: string
  ): Promise<[string, EntityInfo]>;
}

/**
 * 处理器配置接口
 */
export interface ProcessorConfig {
  modalCaptionFunc: LLMModelFunction | VisionModelFunction;
  maxRetries?: number;
  timeout?: number;
}

/**
 * 图像处理器配置
 */
export interface ImageProcessorConfig extends ProcessorConfig {
  modalCaptionFunc: VisionModelFunction;
  imageAnalysisPrompt?: string;
  entityExtractionPrompt?: string;
}

/**
 * 表格处理器配置
 */
export interface TableProcessorConfig extends ProcessorConfig {
  modalCaptionFunc: LLMModelFunction;
  tableAnalysisPrompt?: string;
  entityExtractionPrompt?: string;
}

/**
 * 公式处理器配置
 */
export interface EquationProcessorConfig extends ProcessorConfig {
  modalCaptionFunc: LLMModelFunction;
  equationAnalysisPrompt?: string;
  entityExtractionPrompt?: string;
}

/**
 * 通用处理器配置
 */
export interface GenericProcessorConfig extends ProcessorConfig {
  modalCaptionFunc: LLMModelFunction;
  genericAnalysisPrompt?: string;
  entityExtractionPrompt?: string;
}

/**
 * 处理器工厂接口
 */
export interface ProcessorFactory {
  createImageProcessor(config: ImageProcessorConfig): BaseProcessor;
  createTableProcessor(config: TableProcessorConfig): BaseProcessor;
  createEquationProcessor(config: EquationProcessorConfig): BaseProcessor;
  createGenericProcessor(config: GenericProcessorConfig): BaseProcessor;
}

/**
 * 处理器管理器接口
 */
export interface ProcessorManager {
  getProcessor(contentType: ContentType): BaseProcessor | undefined;
  registerProcessor(contentType: ContentType, processor: BaseProcessor): void;
  hasProcessor(contentType: ContentType): boolean;
  listProcessors(): ContentType[];
}

/**
 * 处理结果接口
 */
export interface ProcessResult {
  enhancedCaption: string;
  entityInfo: EntityInfo;
  processingTime: number;
  success: boolean;
  error?: string;
}

/**
 * 批处理结果接口
 */
export interface BatchProcessResult {
  total: number;
  success: number;
  failed: number;
  results: ProcessResult[];
  failedItems: Array<{
    content: MultimodalContent;
    error: string;
  }>;
}

/**
 * 处理器统计信息
 */
export interface ProcessorStats {
  totalProcessed: number;
  successCount: number;
  failureCount: number;
  averageProcessingTime: number;
  contentTypeDistribution: Record<ContentType, number>;
}

/**
 * 处理器性能指标
 */
export interface ProcessorMetrics {
  processingTime: number;
  memoryUsage: number;
  cpuUsage?: number;
  errorRate: number;
  throughput: number; // items per second
}
