/**
 * 通用模态处理器
 */

import { 
  MultimodalContent, 
  EntityInfo, 
  ContentType,
  LLMModelFunction,
  ProcessError
} from '@/types';
import { GenericProcessorConfig } from '@/types/processors';
import { BaseModalProcessor } from './BaseModalProcessor';
import { getModuleLogger } from '@/utils/logger';

const logger = getModuleLogger('GenericModalProcessor');

/**
 * 通用模态处理器
 * 用于处理未知类型或自定义类型的内容
 */
export class GenericModalProcessor extends BaseModalProcessor {
  private llmModelFunc: LLMModelFunction;

  constructor(config: GenericProcessorConfig) {
    super(config);
    this.llmModelFunc = config.modalCaptionFunc;
  }

  /**
   * 处理通用内容
   */
  async processMultimodalContent(
    modalContent: MultimodalContent,
    contentType: ContentType,
    filePath?: string,
    entityName?: string
  ): Promise<[string, EntityInfo]> {
    this.validateContent(modalContent);
    
    const startTime = Date.now();

    try {
      logger.info(`Processing generic content of type: ${contentType}`);

      // 生成实体名称
      const finalEntityName = entityName || this.generateDefaultEntityName(modalContent, filePath);

      // 生成增强描述
      const enhancedDescription = await this.generateGenericDescription(modalContent, contentType);

      // 提取实体信息
      const entityInfo = await this.extractEntityInfo(
        enhancedDescription,
        finalEntityName,
        'GENERIC_ENTITY_EXTRACTION'
      );

      // 创建实体和文本块
      const result = await this.createEntityAndChunk(
        enhancedDescription,
        entityInfo,
        filePath || 'unknown'
      );

      const processingTime = Date.now() - startTime;
      logger.info(`Generic content processing completed in ${processingTime}ms`);

      return result;

    } catch (error) {
      logger.error('Failed to process generic content:', error);
      throw new ProcessError(
        `Failed to process generic content: ${error instanceof Error ? error.message : String(error)}`,
        contentType
      );
    }
  }

  /**
   * 生成通用描述
   */
  private async generateGenericDescription(
    modalContent: MultimodalContent,
    contentType: ContentType
  ): Promise<string> {
    try {
      // 分析内容结构
      const contentAnalysis = this.analyzeContentStructure(modalContent, contentType);
      
      // 生成增强描述
      const enhancedDescription = await this.generateEnhancedDescription(
        modalContent,
        'GENERIC_ANALYSIS'
      );

      // 合并结构分析和AI生成的描述
      return this.combineGenericAnalysis(contentAnalysis, enhancedDescription);

    } catch (error) {
      logger.error('Failed to generate generic description:', error);
      
      // 返回基础描述
      return this.generateFallbackDescription(modalContent, contentType);
    }
  }

  /**
   * 分析内容结构
   */
  private analyzeContentStructure(modalContent: MultimodalContent, contentType: ContentType): any {
    const analysis = {
      type: contentType,
      size: 0,
      properties: [] as string[],
      hasText: false,
      hasMetadata: false,
      summary: ''
    };

    try {
      // 分析内容属性
      const properties = Object.keys(modalContent);
      analysis.properties = properties.filter(prop => prop !== 'type');
      
      // 检查是否有文本内容
      analysis.hasText = this.hasTextContent(modalContent);
      
      // 检查是否有元数据
      analysis.hasMetadata = this.hasMetadata(modalContent);
      
      // 计算内容大小
      analysis.size = this.calculateContentSize(modalContent);
      
      // 生成摘要
      analysis.summary = this.generateContentSummary(analysis, modalContent);

    } catch (error) {
      logger.warn('Failed to analyze content structure:', error);
    }

    return analysis;
  }

  /**
   * 检查是否有文本内容
   */
  private hasTextContent(content: MultimodalContent): boolean {
    const textFields = ['text', 'content', 'description', 'caption', 'footnote'];
    
    return textFields.some(field => {
      const value = (content as any)[field];
      return value && typeof value === 'string' && value.trim().length > 0;
    });
  }

  /**
   * 检查是否有元数据
   */
  private hasMetadata(content: MultimodalContent): boolean {
    const metadataFields = ['id', 'bbox', 'timestamp', 'source', 'author', 'created_at'];
    
    return metadataFields.some(field => {
      return (content as any)[field] !== undefined;
    });
  }

  /**
   * 计算内容大小
   */
  private calculateContentSize(content: MultimodalContent): number {
    try {
      const jsonString = JSON.stringify(content);
      return jsonString.length;
    } catch (error) {
      return 0;
    }
  }

  /**
   * 生成内容摘要
   */
  private generateContentSummary(analysis: any, content: MultimodalContent): string {
    const parts: string[] = [];
    
    parts.push(`${analysis.type}类型内容`);
    
    if (analysis.properties.length > 0) {
      parts.push(`包含属性: ${analysis.properties.join(', ')}`);
    }
    
    if (analysis.hasText) {
      parts.push('包含文本内容');
    }
    
    if (analysis.hasMetadata) {
      parts.push('包含元数据');
    }
    
    if (analysis.size > 0) {
      parts.push(`数据大小: ${analysis.size} 字符`);
    }

    return parts.join('; ');
  }

  /**
   * 合并通用分析结果
   */
  private combineGenericAnalysis(analysis: any, aiDescription: string): string {
    const parts: string[] = [];
    
    // 添加内容类型和基本信息
    parts.push('=== 内容信息 ===');
    parts.push(analysis.summary);
    
    // 添加原始内容（如果不太大）
    if (analysis.size < 1000) {
      parts.push('\n=== 原始内容 ===');
      parts.push(this.formatContentForDisplay(analysis.content));
    }
    
    // 添加AI生成的描述
    if (aiDescription && aiDescription.trim()) {
      parts.push('\n=== 内容分析 ===');
      parts.push(aiDescription);
    }
    
    return parts.join('\n');
  }

  /**
   * 格式化内容用于显示
   */
  private formatContentForDisplay(content: any): string {
    try {
      if (typeof content === 'string') {
        return content;
      }
      
      return JSON.stringify(content, null, 2);
    } catch (error) {
      return String(content);
    }
  }

  /**
   * 生成备用描述
   */
  private generateFallbackDescription(modalContent: MultimodalContent, contentType: ContentType): string {
    const parts: string[] = [];
    
    parts.push(`内容类型: ${contentType}`);
    
    // 尝试提取任何可用的文本内容
    const textContent = this.extractAnyTextContent(modalContent);
    if (textContent) {
      parts.push('内容:');
      parts.push(textContent);
    }
    
    // 添加属性信息
    const properties = Object.keys(modalContent).filter(key => key !== 'type');
    if (properties.length > 0) {
      parts.push(`属性: ${properties.join(', ')}`);
    }
    
    // 添加原始数据（截断）
    const jsonString = JSON.stringify(modalContent);
    if (jsonString.length > 200) {
      parts.push(`数据预览: ${jsonString.substring(0, 200)}...`);
    } else {
      parts.push(`数据: ${jsonString}`);
    }

    return parts.join('\n');
  }

  /**
   * 提取任何文本内容
   */
  private extractAnyTextContent(content: MultimodalContent): string {
    const textFields = [
      'text', 'content', 'description', 'caption', 'footnote',
      'title', 'summary', 'body', 'message', 'value'
    ];
    
    for (const field of textFields) {
      const value = (content as any)[field];
      if (value && typeof value === 'string' && value.trim().length > 0) {
        return value.trim();
      }
      
      // 检查数组类型的文本字段
      if (Array.isArray(value) && value.length > 0) {
        const textItems = value.filter(item => typeof item === 'string' && item.trim().length > 0);
        if (textItems.length > 0) {
          return textItems.join(', ');
        }
      }
    }
    
    return '';
  }

  /**
   * 提取内容变量（重写基类方法）
   */
  protected extractVariablesFromContent(content: MultimodalContent): Record<string, string> {
    const variables: Record<string, string> = {};
    
    // 设置内容类型
    variables.content_type = content.type;
    
    // 尝试提取文本内容
    const textContent = this.extractAnyTextContent(content);
    variables.content_data = textContent || JSON.stringify(content);
    
    // 添加其他可能有用的字段
    const additionalFields = ['id', 'title', 'description', 'source'];
    additionalFields.forEach(field => {
      const value = (content as any)[field];
      if (value && typeof value === 'string') {
        variables[field] = value;
      }
    });
    
    return variables;
  }

  /**
   * 处理自定义内容类型
   */
  async processCustomContent(
    content: any,
    customType: string,
    filePath?: string,
    entityName?: string
  ): Promise<[string, EntityInfo]> {
    // 将自定义内容包装为MultimodalContent
    const wrappedContent: MultimodalContent = {
      type: 'generic' as ContentType,
      ...content
    };
    
    return await this.processMultimodalContent(
      wrappedContent,
      'generic',
      filePath,
      entityName
    );
  }

  /**
   * 获取支持的内容类型
   */
  static getSupportedTypes(): string[] {
    return [
      'generic', 'custom', 'unknown', 'mixed',
      'audio', 'video', 'document', 'data',
      'chart', 'diagram', 'annotation'
    ];
  }

  /**
   * 检查是否可以处理指定类型
   */
  static canProcess(contentType: string): boolean {
    return this.getSupportedTypes().includes(contentType) || contentType === 'generic';
  }
}
