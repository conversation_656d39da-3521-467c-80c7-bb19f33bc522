/**
 * 日志工具
 */

import winston from 'winston';
import path from 'path';

export interface LoggerConfig {
  level?: string;
  filename?: string;
  maxsize?: number;
  maxFiles?: number;
  colorize?: boolean;
  timestamp?: boolean;
}

/**
 * 创建日志记录器
 */
export function createLogger(name: string, config: LoggerConfig = {}): winston.Logger {
  const {
    level = 'info',
    filename,
    maxsize = 5242880, // 5MB
    maxFiles = 5,
    colorize = true,
    timestamp = true,
  } = config;

  const formats = [
    winston.format.errors({ stack: true }),
    winston.format.timestamp(),
    winston.format.printf(({ timestamp, level, message, stack, ...meta }) => {
      let log = `${timestamp} [${name}] ${level}: ${message}`;
      
      if (Object.keys(meta).length > 0) {
        log += ` ${JSON.stringify(meta)}`;
      }
      
      if (stack) {
        log += `\n${stack}`;
      }
      
      return log;
    }),
  ];

  if (colorize) {
    formats.unshift(winston.format.colorize());
  }

  const transports: winston.transport[] = [
    new winston.transports.Console({
      level,
      format: winston.format.combine(...formats),
    }),
  ];

  if (filename) {
    transports.push(
      new winston.transports.File({
        filename,
        level,
        maxsize,
        maxFiles,
        format: winston.format.combine(
          winston.format.timestamp(),
          winston.format.json()
        ),
      })
    );
  }

  return winston.createLogger({
    level,
    transports,
    exitOnError: false,
  });
}

/**
 * 默认日志记录器
 */
export const logger = createLogger('RAGAnything', {
  level: process.env.LOG_LEVEL || 'info',
  filename: process.env.LOG_FILE,
});

/**
 * 设置日志级别
 */
export function setLogLevel(level: string): void {
  logger.level = level;
}

/**
 * 为特定模块创建日志记录器
 */
export function getModuleLogger(moduleName: string): winston.Logger {
  return createLogger(moduleName, {
    level: process.env.LOG_LEVEL || 'info',
  });
}
