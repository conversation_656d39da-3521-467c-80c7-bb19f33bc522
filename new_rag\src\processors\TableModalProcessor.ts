/**
 * 表格模态处理器
 */

import { 
  MultimodalContent, 
  EntityInfo, 
  ContentType,
  TableContent,
  LLMModelFunction,
  ProcessError
} from '@/types';
import { TableProcessorConfig } from '@/types/processors';
import { BaseModalProcessor } from './BaseModalProcessor';
import { getModuleLogger } from '@/utils/logger';

const logger = getModuleLogger('TableModalProcessor');

/**
 * 表格模态处理器
 */
export class TableModalProcessor extends BaseModalProcessor {
  private llmModelFunc: LLMModelFunction;

  constructor(config: TableProcessorConfig) {
    super(config);
    this.llmModelFunc = config.modalCaptionFunc;
  }

  /**
   * 处理表格内容
   */
  async processMultimodalContent(
    modalContent: MultimodalContent,
    contentType: ContentType,
    filePath?: string,
    entityName?: string
  ): Promise<[string, EntityInfo]> {
    this.validateContent(modalContent);
    
    if (contentType !== 'table') {
      throw new ProcessError(`Expected table content, got ${contentType}`, contentType);
    }

    const tableContent = modalContent as TableContent;
    const startTime = Date.now();

    try {
      logger.info(`Processing table content with ${this.getTableRowCount(tableContent)} rows`);

      // 生成实体名称
      const finalEntityName = entityName || this.generateDefaultEntityName(tableContent, filePath);

      // 生成增强描述
      const enhancedDescription = await this.generateTableDescription(tableContent);

      // 提取实体信息
      const entityInfo = await this.extractEntityInfo(
        enhancedDescription,
        finalEntityName,
        'TABLE_ENTITY_EXTRACTION'
      );

      // 创建实体和文本块
      const result = await this.createEntityAndChunk(
        enhancedDescription,
        entityInfo,
        filePath || 'unknown'
      );

      const processingTime = Date.now() - startTime;
      logger.info(`Table processing completed in ${processingTime}ms`);

      return result;

    } catch (error) {
      logger.error('Failed to process table content:', error);
      throw new ProcessError(
        `Failed to process table content: ${error instanceof Error ? error.message : String(error)}`,
        'table'
      );
    }
  }

  /**
   * 生成表格描述
   */
  private async generateTableDescription(tableContent: TableContent): Promise<string> {
    try {
      // 分析表格结构
      const tableAnalysis = this.analyzeTableStructure(tableContent);
      
      // 生成增强描述
      const enhancedDescription = await this.generateEnhancedDescription(
        tableContent,
        'TABLE_ANALYSIS'
      );

      // 合并结构分析和AI生成的描述
      return this.combineTableAnalysis(tableAnalysis, enhancedDescription);

    } catch (error) {
      logger.error('Failed to generate table description:', error);
      
      // 返回基础描述
      return this.generateFallbackDescription(tableContent);
    }
  }

  /**
   * 分析表格结构
   */
  private analyzeTableStructure(tableContent: TableContent): any {
    const analysis = {
      rowCount: 0,
      columnCount: 0,
      hasHeader: false,
      dataTypes: [] as string[],
      summary: ''
    };

    try {
      const tableBody = tableContent.table_body;
      if (!tableBody) {
        return analysis;
      }

      // 解析表格内容（假设是Markdown格式）
      const lines = tableBody.split('\n').filter(line => line.trim());
      
      if (lines.length === 0) {
        return analysis;
      }

      // 计算行数和列数
      const dataRows = lines.filter(line => !line.includes('---') && line.includes('|'));
      analysis.rowCount = dataRows.length;

      if (dataRows.length > 0) {
        const firstRow = dataRows[0];
        analysis.columnCount = (firstRow.match(/\|/g) || []).length - 1;
      }

      // 检查是否有表头
      analysis.hasHeader = lines.some(line => line.includes('---'));

      // 分析数据类型
      analysis.dataTypes = this.inferDataTypes(dataRows);

      // 生成摘要
      analysis.summary = this.generateTableSummary(analysis, tableContent);

    } catch (error) {
      logger.warn('Failed to analyze table structure:', error);
    }

    return analysis;
  }

  /**
   * 推断数据类型
   */
  private inferDataTypes(rows: string[]): string[] {
    const types: string[] = [];
    
    if (rows.length === 0) {
      return types;
    }

    // 获取第一行来确定列数
    const firstRow = rows[0];
    const columns = firstRow.split('|').map(cell => cell.trim()).filter(cell => cell);
    
    for (let i = 0; i < columns.length; i++) {
      let isNumeric = true;
      let isDate = true;
      let isEmpty = true;

      // 检查每一列的数据类型
      for (const row of rows.slice(1)) { // 跳过可能的表头
        const cells = row.split('|').map(cell => cell.trim()).filter(cell => cell);
        if (cells[i]) {
          isEmpty = false;
          const cellValue = cells[i];
          
          // 检查是否为数字
          if (isNumeric && isNaN(Number(cellValue.replace(/[,\s]/g, '')))) {
            isNumeric = false;
          }
          
          // 检查是否为日期
          if (isDate && isNaN(Date.parse(cellValue))) {
            isDate = false;
          }
        }
      }

      if (isEmpty) {
        types.push('empty');
      } else if (isNumeric) {
        types.push('numeric');
      } else if (isDate) {
        types.push('date');
      } else {
        types.push('text');
      }
    }

    return types;
  }

  /**
   * 生成表格摘要
   */
  private generateTableSummary(analysis: any, tableContent: TableContent): string {
    const parts: string[] = [];
    
    parts.push(`表格包含 ${analysis.rowCount} 行 ${analysis.columnCount} 列`);
    
    if (analysis.hasHeader) {
      parts.push('包含表头');
    }
    
    if (analysis.dataTypes.length > 0) {
      const typeCount = analysis.dataTypes.reduce((acc: any, type: string) => {
        acc[type] = (acc[type] || 0) + 1;
        return acc;
      }, {});
      
      const typeDesc = Object.entries(typeCount)
        .map(([type, count]) => `${count}个${type}列`)
        .join(', ');
      parts.push(`数据类型: ${typeDesc}`);
    }
    
    if (tableContent.table_caption && tableContent.table_caption.length > 0) {
      parts.push(`标题: ${tableContent.table_caption.join(', ')}`);
    }

    return parts.join('; ');
  }

  /**
   * 合并表格分析结果
   */
  private combineTableAnalysis(analysis: any, aiDescription: string): string {
    const parts: string[] = [];
    
    // 添加结构化分析
    parts.push('=== 表格结构分析 ===');
    parts.push(analysis.summary);
    
    // 添加AI生成的描述
    if (aiDescription && aiDescription.trim()) {
      parts.push('\n=== 内容分析 ===');
      parts.push(aiDescription);
    }
    
    return parts.join('\n');
  }

  /**
   * 生成备用描述
   */
  private generateFallbackDescription(tableContent: TableContent): string {
    const parts: string[] = [];
    
    if (tableContent.table_body) {
      const rowCount = this.getTableRowCount(tableContent);
      parts.push(`表格数据 (${rowCount} 行)`);
      
      // 添加表格内容的前几行作为示例
      const lines = tableContent.table_body.split('\n').slice(0, 5);
      if (lines.length > 0) {
        parts.push('内容预览:');
        parts.push(...lines);
      }
    }
    
    if (tableContent.table_caption && tableContent.table_caption.length > 0) {
      parts.push(`标题: ${tableContent.table_caption.join(', ')}`);
    }
    
    if (tableContent.table_footnote && tableContent.table_footnote.length > 0) {
      parts.push(`注释: ${tableContent.table_footnote.join(', ')}`);
    }
    
    if (tableContent.bbox) {
      parts.push(`位置: [${tableContent.bbox.join(', ')}]`);
    }

    return parts.join('\n');
  }

  /**
   * 获取表格行数
   */
  private getTableRowCount(tableContent: TableContent): number {
    if (!tableContent.table_body) {
      return 0;
    }
    
    const lines = tableContent.table_body.split('\n').filter(line => 
      line.trim() && line.includes('|') && !line.includes('---')
    );
    
    return lines.length;
  }

  /**
   * 验证表格内容
   */
  protected validateContent(content: MultimodalContent): void {
    super.validateContent(content);
    
    const tableContent = content as TableContent;
    
    if (!tableContent.table_body) {
      throw new ProcessError('Invalid table content: table_body is required', 'table');
    }
  }

  /**
   * 转换表格为CSV格式
   */
  private convertToCSV(tableContent: TableContent): string {
    if (!tableContent.table_body) {
      return '';
    }

    try {
      const lines = tableContent.table_body.split('\n')
        .filter(line => line.trim() && line.includes('|') && !line.includes('---'));
      
      return lines.map(line => {
        const cells = line.split('|')
          .map(cell => cell.trim())
          .filter(cell => cell)
          .map(cell => `"${cell.replace(/"/g, '""')}"`);
        return cells.join(',');
      }).join('\n');

    } catch (error) {
      logger.warn('Failed to convert table to CSV:', error);
      return tableContent.table_body;
    }
  }

  /**
   * 提取表格统计信息
   */
  private extractTableStatistics(tableContent: TableContent): any {
    const stats = {
      totalCells: 0,
      emptyCells: 0,
      numericCells: 0,
      textCells: 0
    };

    try {
      const lines = tableContent.table_body.split('\n')
        .filter(line => line.trim() && line.includes('|') && !line.includes('---'));
      
      for (const line of lines) {
        const cells = line.split('|').map(cell => cell.trim()).filter(cell => cell);
        
        for (const cell of cells) {
          stats.totalCells++;
          
          if (!cell || cell === '') {
            stats.emptyCells++;
          } else if (!isNaN(Number(cell.replace(/[,\s]/g, '')))) {
            stats.numericCells++;
          } else {
            stats.textCells++;
          }
        }
      }

    } catch (error) {
      logger.warn('Failed to extract table statistics:', error);
    }

    return stats;
  }
}
