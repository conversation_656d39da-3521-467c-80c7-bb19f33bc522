/**
 * 文件处理工具函数
 */

import fs from 'fs-extra';
import path from 'path';
import { createHash } from 'crypto';
import mime from 'mime-types';

/**
 * 检查文件是否存在
 */
export async function fileExists(filePath: string): Promise<boolean> {
  try {
    await fs.access(filePath);
    return true;
  } catch {
    return false;
  }
}

/**
 * 确保目录存在
 */
export async function ensureDir(dirPath: string): Promise<void> {
  await fs.ensureDir(dirPath);
}

/**
 * 获取文件扩展名
 */
export function getFileExtension(filePath: string): string {
  return path.extname(filePath).toLowerCase();
}

/**
 * 获取文件名（不含扩展名）
 */
export function getFileNameWithoutExt(filePath: string): string {
  return path.basename(filePath, path.extname(filePath));
}

/**
 * 获取文件MIME类型
 */
export function getMimeType(filePath: string): string | false {
  return mime.lookup(filePath);
}

/**
 * 计算文件MD5哈希
 */
export async function calculateFileHash(filePath: string): Promise<string> {
  const fileBuffer = await fs.readFile(filePath);
  const hashSum = createHash('md5');
  hashSum.update(fileBuffer);
  return hashSum.digest('hex');
}

/**
 * 计算字符串MD5哈希
 */
export function calculateStringHash(content: string, prefix: string = ''): string {
  const hashSum = createHash('md5');
  hashSum.update(content);
  const hash = hashSum.digest('hex');
  return prefix ? `${prefix}${hash}` : hash;
}

/**
 * 获取文件大小
 */
export async function getFileSize(filePath: string): Promise<number> {
  const stats = await fs.stat(filePath);
  return stats.size;
}

/**
 * 读取JSON文件
 */
export async function readJsonFile<T = any>(filePath: string): Promise<T> {
  const content = await fs.readFile(filePath, 'utf-8');
  return JSON.parse(content) as T;
}

/**
 * 写入JSON文件
 */
export async function writeJsonFile(filePath: string, data: any): Promise<void> {
  await ensureDir(path.dirname(filePath));
  await fs.writeFile(filePath, JSON.stringify(data, null, 2), 'utf-8');
}

/**
 * 复制文件
 */
export async function copyFile(src: string, dest: string): Promise<void> {
  await ensureDir(path.dirname(dest));
  await fs.copy(src, dest);
}

/**
 * 移动文件
 */
export async function moveFile(src: string, dest: string): Promise<void> {
  await ensureDir(path.dirname(dest));
  await fs.move(src, dest);
}

/**
 * 删除文件或目录
 */
export async function remove(filePath: string): Promise<void> {
  await fs.remove(filePath);
}

/**
 * 获取目录中的所有文件
 */
export async function getFilesInDirectory(
  dirPath: string,
  extensions?: string[],
  recursive: boolean = false
): Promise<string[]> {
  const files: string[] = [];
  
  const processDirectory = async (currentDir: string): Promise<void> => {
    const items = await fs.readdir(currentDir);
    
    for (const item of items) {
      const itemPath = path.join(currentDir, item);
      const stats = await fs.stat(itemPath);
      
      if (stats.isDirectory() && recursive) {
        await processDirectory(itemPath);
      } else if (stats.isFile()) {
        if (!extensions || extensions.includes(getFileExtension(itemPath))) {
          files.push(itemPath);
        }
      }
    }
  };
  
  await processDirectory(dirPath);
  return files;
}

/**
 * 创建临时文件路径
 */
export function createTempFilePath(extension: string = ''): string {
  const tempDir = process.env.TEMP || process.env.TMP || '/tmp';
  const randomName = Math.random().toString(36).substring(2, 15);
  return path.join(tempDir, `rag_temp_${randomName}${extension}`);
}

/**
 * 清理临时文件
 */
export async function cleanupTempFiles(pattern: string = 'rag_temp_*'): Promise<void> {
  const tempDir = process.env.TEMP || process.env.TMP || '/tmp';
  const files = await fs.readdir(tempDir);
  
  for (const file of files) {
    if (file.includes('rag_temp_')) {
      try {
        await fs.remove(path.join(tempDir, file));
      } catch (error) {
        // 忽略删除错误
      }
    }
  }
}

/**
 * 检查文件是否为支持的格式
 */
export function isSupportedFileFormat(filePath: string): boolean {
  const supportedExtensions = [
    '.pdf',
    '.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp',
    '.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx',
    '.txt', '.md'
  ];
  
  return supportedExtensions.includes(getFileExtension(filePath));
}

/**
 * 获取文件类型分类
 */
export function getFileCategory(filePath: string): 'pdf' | 'image' | 'office' | 'text' | 'unknown' {
  const ext = getFileExtension(filePath);
  
  if (ext === '.pdf') return 'pdf';
  
  if (['.jpg', '.jpeg', '.png', '.bmp', '.tiff', '.tif', '.gif', '.webp'].includes(ext)) {
    return 'image';
  }
  
  if (['.doc', '.docx', '.ppt', '.pptx', '.xls', '.xlsx'].includes(ext)) {
    return 'office';
  }
  
  if (['.txt', '.md'].includes(ext)) {
    return 'text';
  }
  
  return 'unknown';
}
