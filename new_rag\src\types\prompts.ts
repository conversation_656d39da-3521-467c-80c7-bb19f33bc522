/**
 * 提示词模板类型定义
 */

export interface PromptTemplate {
  system?: string;
  user: string;
  variables?: string[];
}

export interface PromptTemplates {
  // 图像处理提示词
  IMAGE_ANALYSIS: PromptTemplate;
  IMAGE_ENTITY_EXTRACTION: PromptTemplate;
  
  // 表格处理提示词
  TABLE_ANALYSIS: PromptTemplate;
  TABLE_ENTITY_EXTRACTION: PromptTemplate;
  
  // 公式处理提示词
  EQUATION_ANALYSIS: PromptTemplate;
  EQUATION_ENTITY_EXTRACTION: PromptTemplate;
  
  // 通用处理提示词
  GENERIC_ANALYSIS: PromptTemplate;
  GENERIC_ENTITY_EXTRACTION: PromptTemplate;
  
  // 实体关系提取
  ENTITY_RELATIONSHIP_EXTRACTION: PromptTemplate;
  
  // 查询相关提示词
  QUERY_UNDERSTANDING: PromptTemplate;
  ANSWER_GENERATION: PromptTemplate;
}

/**
 * 提示词变量替换函数类型
 */
export type PromptVariableReplacer = (template: string, variables: Record<string, string>) => string;

/**
 * 提示词构建器接口
 */
export interface PromptBuilder {
  buildPrompt(templateKey: keyof PromptTemplates, variables: Record<string, string>): string;
  replaceVariables(template: string, variables: Record<string, string>): string;
}
