/**
 * 核心类型定义
 * 定义RAG-Anything系统中使用的所有TypeScript接口和类型
 */

// ============================================================================
// 基础类型
// ============================================================================

export type ContentType = 'text' | 'image' | 'table' | 'equation' | 'generic';

export type ParseMethod = 'auto' | 'ocr' | 'txt';

export type QueryMode = 'local' | 'global' | 'hybrid';

export type ProcessorType = 'image' | 'table' | 'equation' | 'generic';

// ============================================================================
// 文档内容相关类型
// ============================================================================

/**
 * 文档内容块接口
 */
export interface ContentBlock {
  type: ContentType;
  text?: string;
  [key: string]: any;
}

/**
 * 图像内容接口
 */
export interface ImageContent extends ContentBlock {
  type: 'image';
  img_path: string;
  img_caption?: string[];
  img_footnote?: string[];
  img_id?: string;
  bbox?: number[];
}

/**
 * 表格内容接口
 */
export interface TableContent extends ContentBlock {
  type: 'table';
  table_body: string;
  table_caption?: string[];
  table_footnote?: string[];
  table_id?: string;
  bbox?: number[];
}

/**
 * 公式内容接口
 */
export interface EquationContent extends ContentBlock {
  type: 'equation';
  latex: string;
  equation_caption?: string[];
  equation_footnote?: string[];
  equation_id?: string;
  bbox?: number[];
}

/**
 * 文本内容接口
 */
export interface TextContent extends ContentBlock {
  type: 'text';
  text: string;
  text_id?: string;
  bbox?: number[];
}

/**
 * 通用多模态内容类型
 */
export type MultimodalContent = ImageContent | TableContent | EquationContent | TextContent;

// ============================================================================
// 解析相关类型
// ============================================================================

/**
 * 解析结果接口
 */
export interface ParseResult {
  contentList: ContentBlock[];
  mdContent: string;
  metadata?: {
    totalBlocks: number;
    blockTypes: Record<string, number>;
    textLength: number;
  };
}

/**
 * 解析选项接口
 */
export interface ParseOptions {
  method?: ParseMethod;
  lang?: string;
  backend?: string;
  startPage?: number;
  endPage?: number;
  formula?: boolean;
  table?: boolean;
  device?: string;
  source?: string;
}

// ============================================================================
// 实体和知识图谱相关类型
// ============================================================================

/**
 * 实体信息接口
 */
export interface EntityInfo {
  entity_name: string;
  entity_type: string;
  summary: string;
  source_id?: string;
  file_path?: string;
  created_at?: number;
}

/**
 * 文本块数据接口
 */
export interface ChunkData {
  tokens: number;
  content: string;
  chunk_order_index: number;
  full_doc_id: string;
  file_path: string;
}

/**
 * 节点数据接口
 */
export interface NodeData {
  entity_id: string;
  entity_type: string;
  description: string;
  source_id: string;
  file_path: string;
  created_at: number;
}

// ============================================================================
// 配置相关类型
// ============================================================================

/**
 * LLM模型函数类型
 */
export type LLMModelFunction = (
  prompt: string,
  systemPrompt?: string,
  historyMessages?: any[],
  ...args: any[]
) => Promise<string>;

/**
 * 视觉模型函数类型
 */
export type VisionModelFunction = (
  prompt: string,
  systemPrompt?: string,
  historyMessages?: any[],
  imageData?: string,
  ...args: any[]
) => Promise<string>;

/**
 * 嵌入函数类型
 */
export type EmbeddingFunction = (texts: string[]) => Promise<number[][]>;

/**
 * RAGAnything配置接口
 */
export interface RAGAnythingConfig {
  workingDir?: string;
  llmModelFunc?: LLMModelFunction;
  visionModelFunc?: VisionModelFunction;
  embeddingFunc?: EmbeddingFunction;
}

/**
 * 处理选项接口
 */
export interface ProcessOptions {
  outputDir?: string;
  parseMethod?: ParseMethod;
  displayStats?: boolean;
  splitByCharacter?: string;
  splitByCharacterOnly?: boolean;
  docId?: string;
}

/**
 * 批处理选项接口
 */
export interface BatchProcessOptions extends ProcessOptions {
  fileExtensions?: string[];
  recursive?: boolean;
  maxWorkers?: number;
}

// ============================================================================
// 查询相关类型
// ============================================================================

/**
 * 查询参数接口
 */
export interface QueryParams {
  mode?: QueryMode;
  topK?: number;
  includeMetadata?: boolean;
}

/**
 * 查询结果接口
 */
export interface QueryResult {
  answer: string;
  sources?: string[];
  metadata?: any;
}

// ============================================================================
// 错误类型
// ============================================================================

/**
 * RAG错误基类
 */
export class RAGError extends Error {
  constructor(message: string, public code?: string) {
    super(message);
    this.name = 'RAGError';
  }
}

/**
 * 解析错误
 */
export class ParseError extends RAGError {
  constructor(message: string, public filePath?: string) {
    super(message, 'PARSE_ERROR');
    this.name = 'ParseError';
  }
}

/**
 * 处理错误
 */
export class ProcessError extends RAGError {
  constructor(message: string, public contentType?: string) {
    super(message, 'PROCESS_ERROR');
    this.name = 'ProcessError';
  }
}

// ============================================================================
// 工具类型
// ============================================================================

/**
 * 可选属性工具类型
 */
export type Optional<T, K extends keyof T> = Omit<T, K> & Partial<Pick<T, K>>;

/**
 * 深度只读类型
 */
export type DeepReadonly<T> = {
  readonly [P in keyof T]: T[P] extends object ? DeepReadonly<T[P]> : T[P];
};

/**
 * 异步函数返回类型提取器
 */
export type AsyncReturnType<T extends (...args: any) => Promise<any>> = T extends (
  ...args: any
) => Promise<infer R>
  ? R
  : any;
