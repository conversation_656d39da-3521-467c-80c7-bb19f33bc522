/**
 * 提示词模板定义
 */

import { PromptTemplates, PromptTemplate } from '@/types/prompts';

/**
 * 提示词模板集合
 */
export const PROMPTS: PromptTemplates = {
  // 图像分析提示词
  IMAGE_ANALYSIS: {
    system: "你是一个专业的图像分析专家，能够详细描述图像内容、识别关键元素并提供深入的分析。",
    user: `请详细分析这张图像，包括：
1. 图像的主要内容和主题
2. 关键的视觉元素（对象、人物、场景等）
3. 图像的风格、色彩和构图特点
4. 图像可能传达的信息或含义
5. 任何文字内容（如果有的话）

图像描述：{image_description}
图像标题：{image_caption}
图像注释：{image_footnote}

请提供一个全面而准确的分析。`,
    variables: ['image_description', 'image_caption', 'image_footnote']
  },

  IMAGE_ENTITY_EXTRACTION: {
    system: "你是一个知识图谱构建专家，专门从图像内容中提取结构化的实体信息。",
    user: `基于以下图像分析结果，请提取关键实体信息：

图像分析：{enhanced_description}
实体名称：{entity_name}

请以JSON格式返回实体信息，包含：
- entity_name: 实体名称
- entity_type: 实体类型（如：图表、照片、示意图、截图等）
- summary: 实体的简洁摘要描述

JSON格式：
{
  "entity_name": "...",
  "entity_type": "...",
  "summary": "..."
}`,
    variables: ['enhanced_description', 'entity_name']
  },

  // 表格分析提示词
  TABLE_ANALYSIS: {
    system: "你是一个数据分析专家，擅长分析表格数据、识别模式和提取关键信息。",
    user: `请分析以下表格数据：

表格内容：
{table_content}

表格标题：{table_caption}
表格注释：{table_footnote}

请提供详细分析，包括：
1. 表格的结构和组织方式
2. 主要数据类型和字段
3. 数据的趋势、模式或异常
4. 关键统计信息或洞察
5. 表格的目的和意义

请提供全面的分析结果。`,
    variables: ['table_content', 'table_caption', 'table_footnote']
  },

  TABLE_ENTITY_EXTRACTION: {
    system: "你是一个知识图谱构建专家，专门从表格数据中提取结构化的实体信息。",
    user: `基于以下表格分析结果，请提取关键实体信息：

表格分析：{enhanced_description}
实体名称：{entity_name}

请以JSON格式返回实体信息，包含：
- entity_name: 实体名称
- entity_type: 实体类型（如：数据表、统计表、对比表、清单等）
- summary: 实体的简洁摘要描述

JSON格式：
{
  "entity_name": "...",
  "entity_type": "...",
  "summary": "..."
}`,
    variables: ['enhanced_description', 'entity_name']
  },

  // 公式分析提示词
  EQUATION_ANALYSIS: {
    system: "你是一个数学和科学专家，能够解释数学公式、物理方程和化学式的含义。",
    user: `请分析以下数学公式或方程：

LaTeX公式：{equation_latex}
公式标题：{equation_caption}
公式注释：{equation_footnote}

请提供详细分析，包括：
1. 公式的数学含义和用途
2. 各个变量和符号的解释
3. 公式所属的学科领域
4. 公式的应用场景
5. 相关的数学概念或定理

请提供清晰易懂的解释。`,
    variables: ['equation_latex', 'equation_caption', 'equation_footnote']
  },

  EQUATION_ENTITY_EXTRACTION: {
    system: "你是一个知识图谱构建专家，专门从数学公式中提取结构化的实体信息。",
    user: `基于以下公式分析结果，请提取关键实体信息：

公式分析：{enhanced_description}
实体名称：{entity_name}

请以JSON格式返回实体信息，包含：
- entity_name: 实体名称
- entity_type: 实体类型（如：数学公式、物理方程、化学式、定理等）
- summary: 实体的简洁摘要描述

JSON格式：
{
  "entity_name": "...",
  "entity_type": "...",
  "summary": "..."
}`,
    variables: ['enhanced_description', 'entity_name']
  },

  // 通用内容分析提示词
  GENERIC_ANALYSIS: {
    system: "你是一个内容分析专家，能够分析各种类型的内容并提供深入的见解。",
    user: `请分析以下内容：

内容类型：{content_type}
内容数据：{content_data}

请提供详细分析，包括：
1. 内容的主要特征和属性
2. 内容的结构和组织方式
3. 关键信息和要点
4. 内容的价值和意义
5. 可能的应用场景

请提供全面的分析结果。`,
    variables: ['content_type', 'content_data']
  },

  GENERIC_ENTITY_EXTRACTION: {
    system: "你是一个知识图谱构建专家，专门从各种内容中提取结构化的实体信息。",
    user: `基于以下内容分析结果，请提取关键实体信息：

内容分析：{enhanced_description}
实体名称：{entity_name}

请以JSON格式返回实体信息，包含：
- entity_name: 实体名称
- entity_type: 实体类型
- summary: 实体的简洁摘要描述

JSON格式：
{
  "entity_name": "...",
  "entity_type": "...",
  "summary": "..."
}`,
    variables: ['enhanced_description', 'entity_name']
  },

  // 实体关系提取提示词
  ENTITY_RELATIONSHIP_EXTRACTION: {
    system: "你是一个知识图谱专家，专门识别和提取实体之间的关系。",
    user: `请分析以下实体之间的关系：

实体1：{entity1}
实体2：{entity2}
上下文：{context}

请识别这些实体之间的关系类型，如：
- 包含关系（contains, part_of）
- 相关关系（related_to, associated_with）
- 依赖关系（depends_on, requires）
- 时序关系（before, after, during）
- 因果关系（causes, results_in）

请以JSON格式返回关系信息：
{
  "relationship_type": "...",
  "description": "...",
  "confidence": 0.0-1.0
}`,
    variables: ['entity1', 'entity2', 'context']
  },

  // 查询理解提示词
  QUERY_UNDERSTANDING: {
    system: "你是一个查询理解专家，能够分析用户查询的意图和需求。",
    user: `请分析以下用户查询：

查询内容：{query}

请分析：
1. 查询的主要意图和目标
2. 查询中的关键词和概念
3. 查询的类型（事实性、分析性、比较性等）
4. 可能需要的信息类型
5. 查询的复杂度和范围

请提供详细的查询分析结果。`,
    variables: ['query']
  },

  // 答案生成提示词
  ANSWER_GENERATION: {
    system: "你是一个智能问答专家，能够基于检索到的信息生成准确、全面的答案。",
    user: `基于以下检索到的信息，请回答用户的问题：

用户问题：{query}

检索到的相关信息：
{retrieved_info}

请生成一个准确、全面且有用的答案，要求：
1. 直接回答用户的问题
2. 基于提供的信息进行回答
3. 保持逻辑清晰和结构化
4. 如果信息不足，请明确说明
5. 提供相关的补充信息（如果有的话）

答案：`,
    variables: ['query', 'retrieved_info']
  }
};

/**
 * 替换提示词中的变量
 */
export function replacePromptVariables(
  template: string, 
  variables: Record<string, string>
): string {
  let result = template;
  
  for (const [key, value] of Object.entries(variables)) {
    const placeholder = `{${key}}`;
    result = result.replace(new RegExp(placeholder, 'g'), value || '');
  }
  
  return result;
}

/**
 * 构建完整的提示词
 */
export function buildPrompt(
  templateKey: keyof PromptTemplates,
  variables: Record<string, string>
): { system?: string; user: string } {
  const template = PROMPTS[templateKey];
  
  if (!template) {
    throw new Error(`Prompt template not found: ${templateKey}`);
  }
  
  const result: { system?: string; user: string } = {
    user: replacePromptVariables(template.user, variables)
  };
  
  if (template.system) {
    result.system = replacePromptVariables(template.system, variables);
  }
  
  return result;
}
