/**
 * MinerU文档解析器 - TypeScript实现
 * 
 * 提供PDF、图像和Office文档的解析功能
 * 基于MinerU 2.0命令行工具
 */

import path from 'path';
import fs from 'fs-extra';
import { 
  ParseResult, 
  ParseOptions, 
  ContentBlock, 
  ParseError,
  ImageContent,
  TableContent,
  EquationContent,
  TextContent
} from '@/types';
import { 
  executeCommand, 
  buildMineruCommand, 
  checkMineruInstallation,
  checkLibreOfficeInstallation 
} from '@/utils/commandUtils';
import { 
  fileExists, 
  ensureDir, 
  getFileExtension, 
  getFileCategory,
  readJsonFile,
  createTempFilePath
} from '@/utils/fileUtils';
import { getModuleLogger } from '@/utils/logger';

const logger = getModuleLogger('MineruParser');

/**
 * MinerU解析器类
 */
export class MineruParser {
  /**
   * 检查MinerU安装状态
   */
  static async checkInstallation(): Promise<boolean> {
    return await checkMineruInstallation();
  }

  /**
   * 检查LibreOffice安装状态
   */
  static async checkLibreOfficeInstallation(): Promise<boolean> {
    return await checkLibreOfficeInstallation();
  }

  /**
   * 运行MinerU命令
   */
  private static async runMineruCommand(
    inputPath: string,
    outputDir: string,
    options: ParseOptions = {}
  ): Promise<void> {
    const {
      method = 'auto',
      lang,
      backend = 'pipeline',
      startPage,
      endPage,
      formula = true,
      table = true,
      device,
      source = 'huggingface'
    } = options;

    // 确保输出目录存在
    await ensureDir(outputDir);

    // 构建命令参数
    const args = buildMineruCommand(inputPath, outputDir, {
      method,
      lang,
      backend,
      startPage,
      endPage,
      formula,
      table,
      device,
      source
    });

    logger.info(`Running MinerU command: mineru ${args.join(' ')}`);

    try {
      const result = await executeCommand('mineru', args, {
        timeout: 300000, // 5分钟超时
      });

      if (!result.success) {
        throw new ParseError(
          `MinerU command failed: ${result.stderr || result.stdout}`,
          inputPath
        );
      }

      logger.info('MinerU command completed successfully');
    } catch (error) {
      logger.error('MinerU command execution failed:', error);
      throw new ParseError(
        `Failed to execute MinerU: ${error instanceof Error ? error.message : String(error)}`,
        inputPath
      );
    }
  }

  /**
   * 解析MinerU输出结果
   */
  private static async parseMineruOutput(outputDir: string): Promise<ParseResult> {
    const contentList: ContentBlock[] = [];
    let mdContent = '';

    try {
      // 查找输出文件
      const files = await fs.readdir(outputDir);
      const jsonFile = files.find(f => f.endsWith('.json'));
      const mdFile = files.find(f => f.endsWith('.md'));

      // 读取JSON结构化数据
      if (jsonFile) {
        const jsonPath = path.join(outputDir, jsonFile);
        const jsonData = await readJsonFile(jsonPath);
        
        if (Array.isArray(jsonData)) {
          contentList.push(...this.convertJsonToContentBlocks(jsonData));
        } else if (jsonData.content && Array.isArray(jsonData.content)) {
          contentList.push(...this.convertJsonToContentBlocks(jsonData.content));
        }
      }

      // 读取Markdown内容
      if (mdFile) {
        const mdPath = path.join(outputDir, mdFile);
        mdContent = await fs.readFile(mdPath, 'utf-8');
      }

      // 生成元数据
      const blockTypes: Record<string, number> = {};
      contentList.forEach(block => {
        const type = block.type;
        blockTypes[type] = (blockTypes[type] || 0) + 1;
      });

      return {
        contentList,
        mdContent,
        metadata: {
          totalBlocks: contentList.length,
          blockTypes,
          textLength: mdContent.length
        }
      };

    } catch (error) {
      logger.error('Failed to parse MinerU output:', error);
      throw new ParseError(
        `Failed to parse MinerU output: ${error instanceof Error ? error.message : String(error)}`
      );
    }
  }

  /**
   * 将JSON数据转换为内容块
   */
  private static convertJsonToContentBlocks(jsonData: any[]): ContentBlock[] {
    const blocks: ContentBlock[] = [];

    for (const item of jsonData) {
      try {
        const block = this.convertJsonItemToContentBlock(item);
        if (block) {
          blocks.push(block);
        }
      } catch (error) {
        logger.warn('Failed to convert JSON item to content block:', error);
      }
    }

    return blocks;
  }

  /**
   * 将单个JSON项转换为内容块
   */
  private static convertJsonItemToContentBlock(item: any): ContentBlock | null {
    if (!item || typeof item !== 'object') {
      return null;
    }

    const type = item.type || 'text';

    switch (type) {
      case 'text':
        return {
          type: 'text',
          text: item.text || item.content || '',
          text_id: item.id,
          bbox: item.bbox
        } as TextContent;

      case 'image':
        return {
          type: 'image',
          img_path: item.img_path || item.path || '',
          img_caption: item.img_caption || item.caption ? [item.img_caption || item.caption] : [],
          img_footnote: item.img_footnote || item.footnote ? [item.img_footnote || item.footnote] : [],
          img_id: item.img_id || item.id,
          bbox: item.bbox
        } as ImageContent;

      case 'table':
        return {
          type: 'table',
          table_body: item.table_body || item.content || '',
          table_caption: item.table_caption || item.caption ? [item.table_caption || item.caption] : [],
          table_footnote: item.table_footnote || item.footnote ? [item.table_footnote || item.footnote] : [],
          table_id: item.table_id || item.id,
          bbox: item.bbox
        } as TableContent;

      case 'equation':
        return {
          type: 'equation',
          latex: item.latex || item.content || '',
          equation_caption: item.equation_caption || item.caption ? [item.equation_caption || item.caption] : [],
          equation_footnote: item.equation_footnote || item.footnote ? [item.equation_footnote || item.footnote] : [],
          equation_id: item.equation_id || item.id,
          bbox: item.bbox
        } as EquationContent;

      default:
        // 对于未知类型，作为文本处理
        return {
          type: 'text',
          text: item.text || item.content || JSON.stringify(item),
          text_id: item.id,
          bbox: item.bbox
        } as TextContent;
    }
  }

  /**
   * 解析PDF文档
   */
  static async parsePdf(
    pdfPath: string,
    outputDir: string,
    options: ParseOptions = {}
  ): Promise<ParseResult> {
    logger.info(`Parsing PDF: ${pdfPath}`);

    if (!await fileExists(pdfPath)) {
      throw new ParseError(`PDF file not found: ${pdfPath}`, pdfPath);
    }

    if (getFileExtension(pdfPath) !== '.pdf') {
      throw new ParseError(`File is not a PDF: ${pdfPath}`, pdfPath);
    }

    await this.runMineruCommand(pdfPath, outputDir, options);
    return await this.parseMineruOutput(outputDir);
  }

  /**
   * 解析图像文件
   */
  static async parseImage(
    imagePath: string,
    outputDir: string,
    options: ParseOptions = {}
  ): Promise<ParseResult> {
    logger.info(`Parsing image: ${imagePath}`);

    if (!await fileExists(imagePath)) {
      throw new ParseError(`Image file not found: ${imagePath}`, imagePath);
    }

    const category = getFileCategory(imagePath);
    if (category !== 'image') {
      throw new ParseError(`File is not an image: ${imagePath}`, imagePath);
    }

    await this.runMineruCommand(imagePath, outputDir, options);
    return await this.parseMineruOutput(outputDir);
  }

  /**
   * 解析Office文档
   */
  static async parseOfficeDoc(
    docPath: string,
    outputDir: string,
    options: ParseOptions = {}
  ): Promise<ParseResult> {
    logger.info(`Parsing Office document: ${docPath}`);

    if (!await fileExists(docPath)) {
      throw new ParseError(`Office document not found: ${docPath}`, docPath);
    }

    const category = getFileCategory(docPath);
    if (category !== 'office') {
      throw new ParseError(`File is not an Office document: ${docPath}`, docPath);
    }

    // 检查LibreOffice安装
    if (!await this.checkLibreOfficeInstallation()) {
      throw new ParseError(
        'LibreOffice is required for Office document processing but not found',
        docPath
      );
    }

    await this.runMineruCommand(docPath, outputDir, options);
    return await this.parseMineruOutput(outputDir);
  }

  /**
   * 通用文档解析方法
   */
  static async parseDocument(
    filePath: string,
    outputDir: string,
    options: ParseOptions = {}
  ): Promise<ParseResult> {
    logger.info(`Parsing document: ${filePath}`);

    if (!await fileExists(filePath)) {
      throw new ParseError(`File not found: ${filePath}`, filePath);
    }

    const category = getFileCategory(filePath);

    switch (category) {
      case 'pdf':
        return await this.parsePdf(filePath, outputDir, options);
      
      case 'image':
        return await this.parseImage(filePath, outputDir, options);
      
      case 'office':
        return await this.parseOfficeDoc(filePath, outputDir, options);
      
      case 'text':
        // 对于文本文件，直接读取内容
        return await this.parseTextFile(filePath);
      
      default:
        throw new ParseError(`Unsupported file format: ${filePath}`, filePath);
    }
  }

  /**
   * 解析文本文件
   */
  private static async parseTextFile(filePath: string): Promise<ParseResult> {
    const content = await fs.readFile(filePath, 'utf-8');
    
    const contentBlock: TextContent = {
      type: 'text',
      text: content,
      text_id: path.basename(filePath)
    };

    return {
      contentList: [contentBlock],
      mdContent: content,
      metadata: {
        totalBlocks: 1,
        blockTypes: { text: 1 },
        textLength: content.length
      }
    };
  }
}
