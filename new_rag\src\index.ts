/**
 * RAG-Anything TypeScript - 主入口文件
 */

// 导出核心类
export { RAGAnything } from './core';

// 导出解析器
export { MineruParser } from './parsers';

// 导出处理器
export {
  BaseModalProcessor,
  ImageModalProcessor,
  TableModalProcessor,
  EquationModalProcessor,
  GenericModalProcessor
} from './processors';

// 导出工具函数
export {
  createLogger,
  getModuleLogger,
  setLogLevel
} from './utils/logger';

export {
  fileExists,
  ensureDir,
  getFileExtension,
  getFileNameWithoutExt,
  getMimeType,
  calculateFileHash,
  calculateStringHash,
  getFileSize,
  readJsonFile,
  writeJsonFile,
  copyFile,
  moveFile,
  remove,
  getFilesInDirectory,
  createTempFilePath,
  cleanupTempFiles,
  isSupportedFileFormat,
  getFileCategory
} from './utils/fileUtils';

export {
  executeCommand,
  executeShellCommand,
  isCommandAvailable,
  checkMineruInstallation,
  checkLibreOfficeInstallation,
  buildMineruCommand
} from './utils/commandUtils';

export {
  PROMPTS,
  replacePromptVariables,
  buildPrompt
} from './utils/prompts';

// 导出所有类型
export type * from './types';

// 版本信息
export const VERSION = '1.0.0';
export const AUTHOR = 'Zirui Guo (TypeScript port)';
export const DESCRIPTION = 'RAG-Anything: All-in-One RAG System - TypeScript Implementation';

// 默认导出
export default RAGAnything;
